import { pgTable, uuid, text, timestamp, boolean, integer, jsonb, serial } from "drizzle-orm/pg-core"
import { relations, sql } from "drizzle-orm"

// Users table (updated for Better Auth)
export const user = pgTable("user", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified")
    .$defaultFn(() => false)
    .notNull(),
  image: text("image"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
});

// Sessions table (required by Better Auth)
export const session = pgTable("session", {
  id: text("id").primaryKey(),
  expiresAt: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

// Accounts table (required by Better Auth for OAuth)
export const account = pgTable("account", {
  id: text("id").primaryKey(),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
});

// Verification tokens table (required by Better Auth)
export const verificationTokens = pgTable("verification_tokens", {
  id: uuid("id").primaryKey().default(sql`gen_random_uuid()`),
  identifier: text("identifier").notNull(),
  token: text("token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
})

// Folders table
export const folders = pgTable("folders", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull().references(() => user.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  noteCount: integer("note_count").default(0),
  color: text("color").default("#10b981"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

// Verses table (existing)
export const verses = pgTable("verses", {
  id: serial("id").primaryKey(),
  surahNumber: integer("surah_number").notNull(),
  ayahNumber: integer("ayah_number").notNull(),
  verseKeys: text("verse_keys").notNull(),
  textArabic: text("text_arabic").notNull(),
  textClean: text("text_clean"),
})

export const kitabs = pgTable("kitabs", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  translated_title: text("translated_title"),
  author: text("author").notNull(),
  translatedBy: text("translated_by"),
  language: text("language"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

// Tafsirs table (existing)
export const tafsirs = pgTable("tafsirs", {
  id: serial("id").primaryKey(),
  kitabId: integer("kitab_id").notNull().references(() => kitabs.id, { onDelete: "cascade" }),
  verseKeys: text("verse_keys").notNull(),
  text: text("text").notNull(),
  language: text("language"),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
})

// Asbab table (existing)
export const asbab = pgTable("asbab", {
  id: serial("id").primaryKey(),
  kitabId: integer("kitab_id").notNull().references(() => kitabs.id, { onDelete: "cascade" }),
  verseKeys: text("verse_keys").notNull(),
  text: text("text").notNull(),
  language: text("language"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

// Uploads table (existing)
export const uploads = pgTable("uploads", {
  id: serial("id").primaryKey(),
  userId: text("user_id").references(() => user.id),
  attachmentUrl: text("attachment_url").notNull(),
  ocrText: text("ocr_text"),
  detectedVersesKeys: text("detected_verses_keys").array(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

// Notes table
export const notes = pgTable("notes", {
  id: serial("id").primaryKey(),
  verseKeys: text("verse_keys").notNull(),
  userId: text("user_id").notNull().references(() => user.id, { onDelete: "cascade" }),
  folderId: integer("folder_id").references(() => folders.id, { onDelete: "set null" }),
  uploadId: integer("upload_id").references(() => uploads.id, { onDelete: "set null" }),
  tafsirIds: integer("tafsir_ids").array(),
  asbabIds: text("asbab_ids").array(),
  title: text("title").notNull(),
  shortDescription: text("short_description"),
  content: text("content"),
  language: text("language"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

export const flashCards = pgTable("flash_cards", {
  id: serial("id").primaryKey(),
  userId: text("user_id").references(() => user.id, { onDelete: "cascade" }),
  noteId: integer("note_id").references(() => notes.id, { onDelete: "cascade" }),
  question: text("question").notNull(),
  answer: text("answer").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
})

export const quizzes = pgTable("quizzes", {
  id: serial("id").primaryKey(),
  userId: text("user_id").references(() => user.id, { onDelete: "cascade" }),
  noteId: integer("note_id").references(() => notes.id, { onDelete: "cascade" }),
  question: text("question").notNull(),
  quizAnswers: jsonb("quiz_answers").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
})

export const translations = pgTable("translations", {
  id: serial("id").primaryKey(),
  verseKeys: text("verse_keys").notNull(),
  language: text("language").notNull(),
  name: text("name"),
  translator: text("translator"),
  text: text("text").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
})

export const verification = pgTable("verification", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").$defaultFn(
    () => /* @__PURE__ */ new Date()
  ),
  updatedAt: timestamp("updated_at").$defaultFn(
    () => /* @__PURE__ */ new Date()
  ),
});

// Relations
export const userRelations = relations(user, ({ many }) => ({
  folders: many(folders),
  notes: many(notes),
  uploads: many(uploads),
  sessions: many(session),
  accounts: many(account),
  flashCards: many(flashCards),
  quizzes: many(quizzes),
}))

export const sessionRelations = relations(session, ({ one }) => ({
  user: one(user, {
    fields: [session.userId],
    references: [user.id],
  }),
}))

export const accountRelations = relations(account, ({ one }) => ({
  user: one(user, {
    fields: [account.userId],
    references: [user.id],
  }),
}))

export const foldersRelations = relations(folders, ({ one, many }) => ({
  user: one(user, {
    fields: [folders.userId],
    references: [user.id],
  }),
  notes: many(notes),
}))

export const notesRelations = relations(notes, ({ one, many }) => ({
  user: one(user, {
    fields: [notes.userId],
    references: [user.id],
  }),
  folder: one(folders, {
    fields: [notes.folderId],
    references: [folders.id],
  }),
  flashCards: many(flashCards),
  quizzes: many(quizzes),
}))

export const uploadsRelations = relations(uploads, ({ one }) => ({
  user: one(user, {
    fields: [uploads.userId],
    references: [user.id],
  }),
  note: one(notes, {
    fields: [uploads.id],
    references: [notes.uploadId],
  }),
}))

export const flashCardsRelations = relations(flashCards, ({ one }) => ({
  user: one(user, {
    fields: [flashCards.userId],
    references: [user.id],
  }),
  note: one(notes, {
    fields: [flashCards.noteId],
    references: [notes.id],
  }),
}))

export const quizzesRelations = relations(quizzes, ({ one }) => ({
  user: one(user, {
    fields: [quizzes.userId],
    references: [user.id],
  }),
  note: one(notes, {
    fields: [quizzes.noteId],
    references: [notes.id],
  }),
}))

export const tafsirRelations = relations(tafsirs, ({ one }) => ({
  kitab: one(kitabs, {
    fields: [tafsirs.kitabId],
    references: [kitabs.id],
  }),
}))

export const asbabRelations = relations(asbab, ({ one }) => ({
  kitab: one(kitabs, {
    fields: [asbab.kitabId],
    references: [kitabs.id],
  }),
}))
