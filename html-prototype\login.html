<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Tadabbur AI</title>
    <link rel="stylesheet" href="styles/global.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="auth-header">
        <div class="container">
            <a href="index.html" class="logo">
                <img src="../docs/logo.png" alt="Tadabbur AI" class="logo-img">
                <span><PERSON><PERSON><PERSON> AI</span>
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <!-- Form Header -->
                <div class="auth-header-content">
                    <h1>Welcome Back</h1>
                    <p>Sign in to continue your Quranic journey</p>
                </div>

                <!-- Login Form -->
                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="email">Email or Username</label>
                        <input 
                            type="text" 
                            id="email" 
                            name="email" 
                            class="input" 
                            placeholder="Enter your email or username"
                            required
                        >
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="input" 
                                placeholder="Enter your password"
                                required
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <div class="error-message" id="passwordError"></div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember" id="remember">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#forgot-password" class="forgot-link">Forgot password?</a>
                    </div>

                    <button type="submit" class="btn btn-primary auth-submit" id="loginButton">
                        <span class="button-text">Sign In</span>
                        <div class="spinner" style="display: none;"></div>
                    </button>

                    <div class="form-error" id="formError" style="display: none;"></div>
                </form>

                <!-- Divider -->
                <div class="auth-divider">
                    <span>or</span>
                </div>

                <!-- Social Login (Future Feature) -->
                <div class="social-login">
                    <button class="btn btn-secondary social-btn" disabled>
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </button>
                </div>

                <!-- Switch to Register -->
                <div class="auth-switch">
                    <p>Don't have an account? <a href="register.html">Create one here</a></p>
                </div>
            </div>

            <!-- Side Info -->
            <div class="auth-info">
                <div class="info-content">
                    <h2>Deepen Your Understanding</h2>
                    <p>Join thousands of Muslims exploring the Quran with AI-powered insights, personalized study notes, and interactive learning tools.</p>
                    
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-search"></i>
                            <span>Intelligent Quranic search</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-sticky-note"></i>
                            <span>Personalized study notes</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-brain"></i>
                            <span>Interactive learning tools</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-book-reader"></i>
                            <span>Contextual explanations</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Form handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous errors
            clearErrors();
            
            // Get form data
            const formData = new FormData(this);
            const email = formData.get('email').trim();
            const password = formData.get('password');
            
            // Basic validation
            let hasErrors = false;
            
            if (!email) {
                showError('emailError', 'Email or username is required');
                hasErrors = true;
            }
            
            if (!password) {
                showError('passwordError', 'Password is required');
                hasErrors = true;
            }
            
            if (hasErrors) return;
            
            // Show loading state
            showLoading(true);
            
            // Simulate API call
            setTimeout(() => {
                // For demo purposes, redirect to study page
                window.location.href = 'study.html';
            }, 1500);
        });
        
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + 'ToggleIcon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
        
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        function clearErrors() {
            const errorElements = document.querySelectorAll('.error-message, .form-error');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }
        
        function showLoading(isLoading) {
            const button = document.getElementById('loginButton');
            const buttonText = button.querySelector('.button-text');
            const spinner = button.querySelector('.spinner');
            
            if (isLoading) {
                button.disabled = true;
                buttonText.style.display = 'none';
                spinner.style.display = 'block';
            } else {
                button.disabled = false;
                buttonText.style.display = 'block';
                spinner.style.display = 'none';
            }
        }
    </script>
</body>
</html>
