# Tadabbur AI - HTML Prototype

This is an HTML/CSS prototype of the Tadabbur AI application based on the MVP specifications.

## Pages Included

### 1. Home Page (`index.html`)
- Clean, minimalist design with centered search functionality
- Suggestion chips for common queries
- Feature preview cards
- Responsive design

**Features:**
- Large search bar with placeholder text
- Interactive suggestion chips
- Feature preview section
- Mobile-responsive layout

### 2. Authentication Pages
- **Login Page** (`login.html`) - User login form with validation
- **Register Page** (`register.html`) - User registration with password strength checker

**Features:**
- Form validation
- Password visibility toggle
- Loading states
- Responsive design
- Social login placeholder (disabled)

### 3. Study Dashboard (`study.html`)
- Three-column layout: Sidebar, Chat Area, Notes Panel
- Interactive chat interface
- Collapsible notes panel with tabs
- Mobile-responsive with overlay navigation

**Features:**
- Left sidebar with profile, folders, and chat history
- Main chat area with Arabic text support
- Notes panel with Summary, Flashcards, Quiz, and Games tabs
- Mobile-friendly responsive design

## File Structure

```
html-prototype/
├── index.html              # Home page
├── login.html              # Login page
├── register.html           # Register page
├── study.html              # Study dashboard
├── styles/
│   ├── global.css          # Global styles and utilities
│   ├── home.css            # Home page specific styles
│   ├── auth.css            # Authentication pages styles
│   └── study.css           # Study dashboard styles
├── scripts/
│   └── study.js            # Study dashboard JavaScript
└── README.md               # This file
```

## Key Features Implemented

### Design System
- Consistent color palette and typography
- CSS custom properties (variables) for maintainability
- Responsive design with mobile-first approach
- Clean, modern UI components

### Home Page
- Hero section with logo and tagline
- Interactive search functionality
- Suggestion chips that populate the search
- Feature preview cards
- Responsive navigation

### Authentication
- Login and register forms with validation
- Password strength indicator
- Form error handling
- Loading states
- Mobile-responsive design

### Study Dashboard
- Three-panel layout (sidebar, chat, notes)
- Interactive chat with Arabic text support
- Notes generation with multiple tabs
- Collapsible sidebar for mobile
- Chat history and folder organization
- Usage limits display for free tier

### Interactive Elements
- Mobile menu with overlay
- Collapsible notes panel
- Tab switching in notes
- Auto-resizing chat input
- Typing indicators
- Flashcards with flip functionality
- Quiz interface
- Memory games

## Technologies Used

- **HTML5** - Semantic markup
- **CSS3** - Modern styling with Grid, Flexbox, and custom properties
- **JavaScript** - Interactive functionality
- **Font Awesome** - Icons
- **Google Fonts** - Amiri font for Arabic text

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for tablets and mobile devices

## Usage

1. Open `index.html` in a web browser to start
2. Navigate between pages using the links
3. Test the interactive features:
   - Search functionality on home page
   - Form validation on auth pages
   - Chat interface and notes generation on study page
   - Mobile responsive behavior

## Notes

- This is a static HTML prototype for design and UX testing
- Form submissions and API calls are simulated
- Arabic text uses the Amiri font for proper rendering
- All interactive features are implemented with vanilla JavaScript
- The design follows the MVP specifications from `docs/structure.md`

## Next Steps

This prototype can be used as a reference for implementing the actual application with:
- Next.js for the frontend framework
- Better Auth for authentication
- Supabase for the database
- Drizzle ORM for database operations
- AI integration for the chat functionality
