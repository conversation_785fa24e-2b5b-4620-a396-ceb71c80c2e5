<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tadabbur AI - Deep Quranic Contemplation</title>
    <link rel="stylesheet" href="styles/global.css">
    <link rel="stylesheet" href="styles/home.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-book-open"></i>
                    <span>Tadabbur AI</span>
                </div>
                <nav class="nav">
                    <a href="login.html" class="btn btn-ghost">Login</a>
                    <a href="register.html" class="btn btn-primary">Register</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="hero-section">
                <!-- Logo and Tagline -->
                <div class="hero-header">
                    <div class="hero-logo">
                        <i class="fas fa-book-open"></i>
                        <h1>Tadabbur AI</h1>
                    </div>
                    <p class="tagline">Deep Quranic contemplation powered by AI</p>
                </div>

                <!-- Search Section -->
                <div class="search-section">
                    <form class="search-form" action="study.html" method="GET">
                        <div class="search-container">
                            <input 
                                type="text" 
                                name="q"
                                class="search-input" 
                                placeholder="Ask about any verse, concept, or story from the Quran..."
                                required
                            >
                            <button type="submit" class="search-button">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Suggestion Chips -->
                    <div class="suggestion-chips">
                        <button class="chip" onclick="searchSuggestion('Explain Surah Al-Fatiha')">
                            Explain Surah Al-Fatiha
                        </button>
                        <button class="chip" onclick="searchSuggestion('Verses about patience')">
                            Verses about patience
                        </button>
                        <button class="chip" onclick="searchSuggestion('What is jihad in Quran?')">
                            What is jihad in Quran?
                        </button>
                        <button class="chip" onclick="searchSuggestion('Stories of Prophet Yusuf')">
                            Stories of Prophet Yusuf
                        </button>
                        <button class="chip" onclick="searchSuggestion('Meaning of Al-Baqarah 255')">
                            Meaning of Al-Baqarah 255
                        </button>
                    </div>
                </div>

                <!-- Features Preview -->
                <div class="features-preview">
                    <div class="feature-card">
                        <i class="fas fa-search"></i>
                        <h3>Intelligent Search</h3>
                        <p>Ask questions in natural language and get comprehensive answers from the Quran</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-sticky-note"></i>
                        <h3>Study Notes</h3>
                        <p>Generate summaries, flashcards, and quizzes for deeper understanding</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-book"></i>
                        <h3>Contextual Learning</h3>
                        <p>Get verses with translations, tafsir, and historical context</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#about">About</a>
                    <a href="#contact">Contact</a>
                    <a href="#terms">Terms</a>
                    <a href="#privacy">Privacy</a>
                </div>
                <div class="footer-text">
                    <p>&copy; 2024 Tadabbur AI. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Handle suggestion chip clicks
        function searchSuggestion(query) {
            const searchInput = document.querySelector('.search-input');
            searchInput.value = query;
            
            // Trigger form submission
            document.querySelector('.search-form').submit();
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            const searchButton = document.querySelector('.search-button');
            
            // Focus effect
            searchInput.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            searchInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
            
            // Form validation
            document.querySelector('.search-form').addEventListener('submit', function(e) {
                const query = searchInput.value.trim();
                if (!query) {
                    e.preventDefault();
                    searchInput.focus();
                    return false;
                }
                
                // Add loading state
                searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                searchButton.disabled = true;
            });
        });
    </script>
</body>
</html>
