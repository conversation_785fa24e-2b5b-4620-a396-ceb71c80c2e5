/* Home Page Specific Styles */

/* Header */
.header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    font-size: 1.75rem;
}

.nav {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* Main Content */
.main {
    min-height: calc(100vh - 140px);
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
}

.hero-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

/* Hero Header */
.hero-header {
    margin-bottom: var(--spacing-2xl);
}

.hero-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.hero-logo i {
    font-size: 4rem;
    color: var(--primary-color);
}

.hero-logo h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.tagline {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 400;
}

/* Search Section */
.search-section {
    margin-bottom: var(--spacing-2xl);
}

.search-form {
    margin-bottom: var(--spacing-xl);
}

.search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-sm);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.search-container.focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgb(37 99 235 / 0.1), var(--shadow-lg);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1.125rem;
    background: transparent;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
}

.search-button:hover {
    background-color: var(--primary-hover);
    transform: translateX(2px);
}

.search-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Suggestion Chips */
.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
    max-width: 700px;
    margin: 0 auto;
}

.chip {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.chip:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Features Preview */
.features-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.feature-card {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
    margin: 0;
}

/* Footer */
.footer {
    background-color: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xl) 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-text p {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-logo h1 {
        font-size: 2.5rem;
    }
    
    .tagline {
        font-size: 1.125rem;
    }
    
    .search-container {
        margin: 0 var(--spacing-md);
    }
    
    .search-input {
        font-size: 1rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .suggestion-chips {
        margin: 0 var(--spacing-md);
    }
    
    .chip {
        font-size: 0.8125rem;
        padding: var(--spacing-xs) var(--spacing-md);
    }
    
    .features-preview {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        margin: 0 var(--spacing-md);
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .nav {
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .hero-logo h1 {
        font-size: 2rem;
    }
    
    .hero-logo i {
        font-size: 3rem;
    }
    
    .tagline {
        font-size: 1rem;
    }
    
    .search-input::placeholder {
        font-size: 0.875rem;
    }
}
