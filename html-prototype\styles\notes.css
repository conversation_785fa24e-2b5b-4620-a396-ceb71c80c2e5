/* Notes List Page Styles */

/* Main Content */
.notes-main {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--background-color);
}

/* Header */
.notes-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.mobile-menu-btn:hover {
    background-color: var(--surface-color);
}

/* Breadcrumb */
.breadcrumb {
    flex: 1;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 600;
}

.breadcrumb-item {
    color: var(--text-secondary);
}

.breadcrumb-item.current {
    color: var(--text-primary);
}

.breadcrumb-separator {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color 0.2s ease;
}

.user-menu-btn:hover {
    color: var(--primary-color);
}

/* Search Section */
.search-section {
    padding: var(--spacing-lg);
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
}

.search-form {
    max-width: 600px;
    margin: 0 auto;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-sm);
    transition: all 0.3s ease;
}

.search-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1rem;
    background: transparent;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.search-button:hover {
    background-color: var(--primary-hover);
}

/* Notes Container */
.notes-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.notes-list {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Note Item */
.note-item {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.note-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.note-content {
    flex: 1;
    min-width: 0;
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    gap: var(--spacing-md);
}

.note-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.4;
}

.note-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
    flex-shrink: 0;
}

.note-folder {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-muted);
    background-color: var(--surface-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.note-folder i {
    font-size: 0.6875rem;
}

.note-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.note-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-actions {
    display: flex;
    align-items: center;
    color: var(--text-muted);
    font-size: 1.25rem;
    transition: color 0.2s ease;
}

.note-item:hover .note-actions {
    color: var(--primary-color);
}

/* Empty State */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.empty-content {
    max-width: 300px;
}

.empty-content i {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .notes-container {
        padding: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }
    
    .notes-header {
        padding: var(--spacing-md);
    }
    
    .breadcrumb {
        font-size: 1.125rem;
    }
    
    .search-section {
        padding: var(--spacing-md);
    }
    
    .notes-container {
        padding: var(--spacing-md);
    }
    
    .note-item {
        padding: var(--spacing-md);
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }
    
    .note-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .note-meta {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    
    .note-actions {
        align-self: flex-end;
        margin-top: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .breadcrumb {
        font-size: 1rem;
    }
    
    .note-title {
        font-size: 1rem;
    }
    
    .note-description {
        font-size: 0.8125rem;
    }
    
    .header-actions {
        gap: var(--spacing-xs);
    }
    
    .header-actions .btn {
        padding: var(--spacing-sm);
    }
}
