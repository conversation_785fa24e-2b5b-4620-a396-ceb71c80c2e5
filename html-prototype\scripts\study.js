// Study Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileOverlay = document.getElementById('mobileOverlay');
    const notesPanel = document.getElementById('notesPanel');
    const closeNotesBtn = document.getElementById('closeNotesBtn');
    const chatForm = document.getElementById('chatForm');
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');
    const chatMessages = document.getElementById('chatMessages');
    const newChatBtn = document.getElementById('newChatBtn');
    
    // Tab functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    // Mobile menu functionality
    mobileMenuBtn.addEventListener('click', function() {
        sidebar.classList.add('open');
        mobileOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    });
    
    sidebarToggle.addEventListener('click', closeSidebar);
    mobileOverlay.addEventListener('click', closeSidebar);
    
    function closeSidebar() {
        sidebar.classList.remove('open');
        mobileOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    // Notes panel functionality
    closeNotesBtn.addEventListener('click', function() {
        closeNotesPanel();
    });
    
    function openNotesPanel() {
        notesPanel.classList.add('open');
        document.body.classList.add('notes-open');
        
        // Simulate loading and then show content
        setTimeout(() => {
            showNotesContent();
        }, 2000);
    }
    
    function closeNotesPanel() {
        notesPanel.classList.remove('open');
        document.body.classList.remove('notes-open');
    }
    
    function showNotesContent() {
        const summaryTab = document.getElementById('summary');
        summaryTab.innerHTML = `
            <div class="notes-summary">
                <h4>Key Points about Patience in Islam</h4>
                <ul>
                    <li><strong>Sabr (Patience)</strong> is one of the most emphasized virtues in the Quran</li>
                    <li>Patience brings <strong>unlimited reward</strong> from Allah (Az-Zumar 39:10)</li>
                    <li>Believers are tested with various trials to develop patience (Al-Baqarah 2:155)</li>
                    <li>Patience includes maintaining faith during hardships</li>
                    <li>The patient are given <strong>glad tidings</strong> in the Quran</li>
                </ul>
                
                <h4>Key Verses</h4>
                <div class="verse-summary">
                    <p><strong>Al-Baqarah 2:155:</strong> Tests come in forms of fear, hunger, loss of wealth, lives, and fruits</p>
                    <p><strong>Az-Zumar 39:10:</strong> The patient receive reward without measure</p>
                </div>
                
                <h4>Practical Applications</h4>
                <ul>
                    <li>Practice gratitude during difficult times</li>
                    <li>Remember that trials are temporary</li>
                    <li>Seek strength through prayer and remembrance of Allah</li>
                    <li>Trust in Allah's wisdom and timing</li>
                </ul>
            </div>
        `;
    }
    
    // Tab switching
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabBtns.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Load content for other tabs
            loadTabContent(targetTab);
        });
    });
    
    function loadTabContent(tabName) {
        const tabContent = document.getElementById(tabName);
        
        switch(tabName) {
            case 'flashcards':
                if (tabContent.innerHTML.trim() === '<p>Flashcards will appear here</p>') {
                    tabContent.innerHTML = `
                        <div class="flashcard-container">
                            <div class="flashcard">
                                <div class="flashcard-front">
                                    <h4>Question</h4>
                                    <p>What does "Sabr" mean in Islamic context?</p>
                                </div>
                                <div class="flashcard-back" style="display: none;">
                                    <h4>Answer</h4>
                                    <p>Sabr means patience, perseverance, and steadfastness in faith during trials and hardships.</p>
                                </div>
                                <button class="flip-btn" onclick="flipCard(this)">Show Answer</button>
                            </div>
                            <div class="flashcard-nav">
                                <button class="btn btn-secondary">Previous</button>
                                <span>1 of 5</span>
                                <button class="btn btn-secondary">Next</button>
                            </div>
                        </div>
                    `;
                }
                break;
            case 'quiz':
                if (tabContent.innerHTML.trim() === '<p>Quiz questions will appear here</p>') {
                    tabContent.innerHTML = `
                        <div class="quiz-container">
                            <div class="quiz-question">
                                <h4>Question 1 of 3</h4>
                                <p>According to Az-Zumar 39:10, how will the patient be rewarded?</p>
                                <div class="quiz-options">
                                    <label class="quiz-option">
                                        <input type="radio" name="q1" value="a">
                                        <span>With double reward</span>
                                    </label>
                                    <label class="quiz-option">
                                        <input type="radio" name="q1" value="b">
                                        <span>Without account/measure</span>
                                    </label>
                                    <label class="quiz-option">
                                        <input type="radio" name="q1" value="c">
                                        <span>In the afterlife only</span>
                                    </label>
                                    <label class="quiz-option">
                                        <input type="radio" name="q1" value="d">
                                        <span>With worldly success</span>
                                    </label>
                                </div>
                                <button class="btn btn-primary quiz-submit">Submit Answer</button>
                            </div>
                        </div>
                    `;
                }
                break;
            case 'games':
                if (tabContent.innerHTML.trim() === '<p>Memory games will appear here</p>') {
                    tabContent.innerHTML = `
                        <div class="games-container">
                            <div class="memory-game">
                                <h4>Verse Matching Game</h4>
                                <p>Match the Arabic verses with their English translations</p>
                                <div class="game-grid">
                                    <div class="game-card" data-pair="1">
                                        <div class="card-content">وَبَشِّرِ الصَّابِرِينَ</div>
                                    </div>
                                    <div class="game-card" data-pair="1">
                                        <div class="card-content">Give good tidings to the patient</div>
                                    </div>
                                    <div class="game-card" data-pair="2">
                                        <div class="card-content">إِنَّمَا يُوَفَّى الصَّابِرُونَ أَجْرَهُم بِغَيْرِ حِسَابٍ</div>
                                    </div>
                                    <div class="game-card" data-pair="2">
                                        <div class="card-content">The patient will be given their reward without account</div>
                                    </div>
                                </div>
                                <div class="game-stats">
                                    <span>Matches: 0/2</span>
                                    <span>Time: 00:00</span>
                                </div>
                            </div>
                        </div>
                    `;
                }
                break;
        }
    }
    
    // Chat functionality
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = chatInput.value.trim();
        if (!message) return;
        
        // Add user message
        addMessage(message, 'user');
        
        // Clear input
        chatInput.value = '';
        adjustTextareaHeight();
        
        // Show typing indicator
        showTypingIndicator();
        
        // Simulate AI response
        setTimeout(() => {
            hideTypingIndicator();
            addAIResponse();
        }, 2000);
    });
    
    // Auto-resize textarea
    chatInput.addEventListener('input', adjustTextareaHeight);
    
    function adjustTextareaHeight() {
        chatInput.style.height = 'auto';
        chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
    }
    
    function addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        if (type === 'user') {
            messageDiv.innerHTML = `
                <div class="message-content">
                    <p>${content}</p>
                </div>
                <div class="message-time">${time}</div>
            `;
        }
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function hideTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    function addAIResponse() {
        const responseDiv = document.createElement('div');
        responseDiv.className = 'message ai-message';
        
        const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        responseDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <p>Thank you for your follow-up question. I'd be happy to provide more information about that topic. This is a simulated response for the HTML prototype.</p>
            </div>
            <div class="generate-notes-section">
                <button class="btn btn-primary generate-notes-btn" onclick="generateNotes()">
                    <i class="fas fa-sticky-note"></i>
                    Generate Notes
                </button>
            </div>
            <div class="message-time">${time}</div>
        `;
        
        chatMessages.appendChild(responseDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // New chat functionality
    newChatBtn.addEventListener('click', function() {
        if (confirm('Start a new chat? Current conversation will be saved to history.')) {
            // Clear current chat
            chatMessages.innerHTML = '';
            
            // Close notes panel if open
            closeNotesPanel();
            
            // Update header title
            document.querySelector('.header-title h1').textContent = 'New Chat';
            
            // Focus on input
            chatInput.focus();
        }
    });
});

// Global functions for inline event handlers
function generateNotes() {
    const notesPanel = document.getElementById('notesPanel');
    const studyLayout = document.querySelector('.study-layout');
    
    notesPanel.classList.add('open');
    studyLayout.classList.add('notes-open');
    
    // Simulate loading
    setTimeout(() => {
        const summaryTab = document.getElementById('summary');
        summaryTab.innerHTML = `
            <div class="notes-summary">
                <h4>Generated Study Notes</h4>
                <p>Notes have been generated based on the current conversation.</p>
                <ul>
                    <li>Key concepts identified and summarized</li>
                    <li>Flashcards created for memorization</li>
                    <li>Quiz questions prepared for testing</li>
                    <li>Memory games available for interactive learning</li>
                </ul>
            </div>
        `;
    }, 2000);
}

function flipCard(button) {
    const flashcard = button.closest('.flashcard');
    const front = flashcard.querySelector('.flashcard-front');
    const back = flashcard.querySelector('.flashcard-back');
    
    if (front.style.display !== 'none') {
        front.style.display = 'none';
        back.style.display = 'block';
        button.textContent = 'Show Question';
    } else {
        front.style.display = 'block';
        back.style.display = 'none';
        button.textContent = 'Show Answer';
    }
}
