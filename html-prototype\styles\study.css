/* Study Dashboard Styles */

/* Layout */
.study-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    height: 100vh;
    overflow: hidden;
}

.study-layout.notes-open {
    grid-template-columns: 300px 1fr 500px;
}

/* Sidebar */
.sidebar {
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-header .logo .logo-img {
    height: 28px;
    width: auto;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.sidebar-toggle:hover {
    background-color: var(--border-color);
}

/* Folders Section */
.folders-section {
    padding: var(--spacing-lg);
    flex: 1;
    overflow-y: auto;
}

.section-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.folder-list {
    margin-bottom: var(--spacing-lg);
}

.folder-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-xs);
}

.folder-item:hover {
    background-color: var(--border-color);
}

.folder-item.active {
    background-color: var(--primary-color);
    color: white;
}

.folder-item i {
    width: 16px;
    color: var(--text-muted);
}

.folder-item.active i {
    color: white;
}

.folder-item span:first-of-type {
    flex: 1;
}

.folder-item .count {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.folder-item.active .count {
    color: rgba(255, 255, 255, 0.8);
}

.create-folder-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md);
    background: none;
    border: 1px dashed var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.create-folder-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

/* Sidebar Bottom Section */
.sidebar-bottom {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.support-section {
    text-align: center;
}

.support-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.875rem;
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: background-color 0.2s ease;
}

.support-btn:hover {
    background-color: var(--surface-color);
}

.upgrade-section {
    text-align: center;
}

.upgrade-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md);
    background-color: var(--text-primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: var(--spacing-md);
}

.upgrade-btn:hover {
    background-color: var(--text-secondary);
    transform: translateY(-1px);
}

.upgrade-text {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.usage-indicator {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.usage-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.usage-text::before {
    content: '📝';
    font-size: 0.875rem;
}

.usage-bar {
    height: 4px;
    background-color: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    width: 100%;
    background-color: var(--text-primary);
    border-radius: 2px;
}

/* Profile Section */
.profile-section {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-lg);
}

.profile-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.profile-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.profile-details {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.profile-email {
    font-size: 0.75rem;
    color: var(--text-muted);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.profile-settings {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.profile-settings:hover {
    color: var(--text-secondary);
    background-color: var(--surface-color);
}



/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.mobile-menu-btn:hover {
    background-color: var(--surface-color);
}

.header-title {
    flex: 1;
}

.header-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color 0.2s ease;
}

.user-menu-btn:hover {
    color: var(--primary-color);
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Messages */
.message {
    display: flex;
    gap: var(--spacing-md);
    max-width: 100%;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-content {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    max-width: 70%;
}

.ai-message .message-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    max-width: 85%;
}

.message-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.message-content {
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    line-height: 1.6;
}

.message-content p {
    margin-bottom: var(--spacing-md);
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Arabic Verses */
.arabic-verse {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
}

.arabic-text {
    font-family: var(--font-arabic);
    font-size: 1.5rem;
    line-height: 2;
    text-align: right;
    direction: rtl;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.translation {
    font-style: italic;
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

.verse-section {
    margin: var(--spacing-lg) 0;
}

.verse-section h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.sources {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.sources h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.sources ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sources li {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-md);
    position: relative;
}

.sources li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.message-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.generate-notes-btn {
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-left: auto;
}

/* Generate Notes Section */
.generate-notes-section {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    position: relative;
}

.generate-notes-section::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    z-index: -1;
    opacity: 0.1;
}

.generate-notes-btn {
    font-size: 0.875rem;
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 1;
}

.generate-notes-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Chat Input */
.chat-input-container {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-md);
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
}

.chat-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    min-height: 24px;
}

.send-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: background-color 0.2s ease;
}

.send-btn:hover {
    background-color: var(--primary-hover);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Notes Panel */
.notes-panel {
    background-color: var(--background-color);
    border-left: 1px solid var(--border-color);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.notes-panel.open {
    display: flex;
}

.notes-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notes-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-notes-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease;
}

.close-notes-btn:hover {
    background-color: var(--surface-color);
}

.notes-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background-color: var(--surface-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.notes-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.loading-state .spinner {
    margin-bottom: var(--spacing-md);
}

.loading-state p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.notes-actions {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.save-notes-btn {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Mobile Styles */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

@media (max-width: 1024px) {
    .study-layout {
        grid-template-columns: 1fr;
    }

    .study-layout.notes-open {
        grid-template-columns: 1fr 500px;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: -300px;
        width: 300px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .mobile-menu-btn {
        display: block;
    }

    .mobile-overlay.show {
        display: block;
    }
}

@media (max-width: 768px) {
    .study-layout.notes-open {
        grid-template-columns: 1fr;
    }

    .notes-panel {
        position: fixed;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1001;
        transition: right 0.3s ease;
    }

    .notes-panel.open {
        right: 0;
    }

    .main-header {
        padding: var(--spacing-md);
    }

    .header-title h1 {
        font-size: 1.25rem;
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .message-content {
        padding: var(--spacing-md);
    }

    .arabic-text {
        font-size: 1.25rem;
    }

    .chat-input-container {
        padding: var(--spacing-md);
    }

    .user-message .message-content,
    .ai-message .message-content {
        max-width: 90%;
    }
}

@media (max-width: 480px) {
    .profile-info {
        gap: var(--spacing-sm);
    }

    .profile-name {
        font-size: 0.8125rem;
    }

    .profile-email {
        font-size: 0.6875rem;
    }

    .header-actions {
        gap: var(--spacing-xs);
    }

    .header-actions .btn {
        padding: var(--spacing-sm);
    }

    .notes-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        min-width: 50%;
    }

    .upgrade-text {
        font-size: 0.6875rem;
    }

    .usage-text {
        font-size: 0.6875rem;
    }
}

/* Interactive Elements */
.typing-indicator .message-content {
    padding: var(--spacing-md) var(--spacing-lg);
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background-color: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Notes Content Styles */
.notes-summary h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-md);
}

.notes-summary ul {
    list-style: none;
    padding: 0;
    margin-bottom: var(--spacing-lg);
}

.notes-summary li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: var(--spacing-lg);
}

.notes-summary li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.verse-summary {
    background-color: var(--surface-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
}

.verse-summary p {
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Flashcard Styles */
.flashcard-container {
    max-width: 100%;
}

.flashcard {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.flashcard h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.flashcard p {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.flip-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.flip-btn:hover {
    background-color: var(--primary-hover);
}

.flashcard-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

/* Quiz Styles */
.quiz-container {
    max-width: 100%;
}

.quiz-question h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.quiz-question p {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.quiz-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-option:hover {
    background-color: var(--border-color);
}

.quiz-option input[type="radio"] {
    margin: 0;
}

.quiz-submit {
    width: 100%;
    padding: var(--spacing-md);
}

/* Games Styles */
.games-container {
    max-width: 100%;
}

.memory-game h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.memory-game p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
}

.game-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.game-card {
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-card:hover {
    border-color: var(--primary-color);
    background-color: var(--background-color);
}

.game-card.matched {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.card-content {
    font-size: 0.875rem;
    line-height: 1.4;
}

.game-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--surface-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}
