/* Study Dashboard Styles */

/* Layout */
.study-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    height: 100vh;
    overflow: hidden;
}

.study-layout.notes-open {
    grid-template-columns: 300px 1fr 400px;
}

/* Sidebar */
.sidebar {
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.sidebar-toggle:hover {
    background-color: var(--border-color);
}

/* Profile Section */
.profile-section {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.profile-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.profile-avatar {
    width: 48px;
    height: 48px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.profile-details h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.plan-badge {
    background-color: var(--accent-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.usage-limits {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.limit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.limit-label {
    color: var(--text-secondary);
}

.limit-value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.new-chat-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    transition: background-color 0.2s ease;
}

.new-chat-btn:hover {
    background-color: var(--primary-hover);
}

.nav-section {
    margin-bottom: var(--spacing-xl);
}

.nav-section h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.folder-tree, .chat-history {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.folder-item, .chat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
}

.folder-item:hover, .chat-item:hover {
    background-color: var(--border-color);
}

.folder-item i {
    color: var(--text-muted);
    width: 16px;
}

.folder-item .count {
    margin-left: auto;
    color: var(--text-muted);
    font-size: 0.75rem;
}

.chat-item {
    flex-direction: column;
    align-items: flex-start;
}

.chat-item.active {
    background-color: var(--primary-color);
    color: white;
}

.chat-title {
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.4;
}

.chat-item.active .chat-title,
.chat-item.active .chat-time {
    color: white;
}

.chat-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Upgrade Prompt */
.upgrade-prompt {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.upgrade-content {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    color: white;
}

.upgrade-content i {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
}

.upgrade-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.upgrade-content p {
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.upgrade-btn {
    background-color: white;
    color: #f59e0b;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.upgrade-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.mobile-menu-btn:hover {
    background-color: var(--surface-color);
}

.header-title {
    flex: 1;
}

.header-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-menu-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.5rem;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color 0.2s ease;
}

.user-menu-btn:hover {
    color: var(--primary-color);
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Messages */
.message {
    display: flex;
    gap: var(--spacing-md);
    max-width: 100%;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-content {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    max-width: 70%;
}

.ai-message .message-content {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    max-width: 85%;
}

.message-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.message-content {
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    line-height: 1.6;
}

.message-content p {
    margin-bottom: var(--spacing-md);
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Arabic Verses */
.arabic-verse {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
}

.arabic-text {
    font-family: var(--font-arabic);
    font-size: 1.5rem;
    line-height: 2;
    text-align: right;
    direction: rtl;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.translation {
    font-style: italic;
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

.verse-section {
    margin: var(--spacing-lg) 0;
}

.verse-section h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.sources {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.sources h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.sources ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sources li {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-md);
    position: relative;
}

.sources li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
}

.message-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.generate-notes-btn {
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-left: auto;
}

/* Chat Input */
.chat-input-container {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--spacing-md);
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
}

.chat-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    min-height: 24px;
}

.send-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: background-color 0.2s ease;
}

.send-btn:hover {
    background-color: var(--primary-hover);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Notes Panel */
.notes-panel {
    background-color: var(--background-color);
    border-left: 1px solid var(--border-color);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.notes-panel.open {
    display: flex;
}

.notes-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notes-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-notes-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease;
}

.close-notes-btn:hover {
    background-color: var(--surface-color);
}

.notes-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background-color: var(--surface-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.notes-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
}

.loading-state .spinner {
    margin-bottom: var(--spacing-md);
}

.loading-state p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.notes-actions {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.save-notes-btn {
    width: 100%;
    padding: var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Mobile Styles */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

@media (max-width: 1024px) {
    .study-layout {
        grid-template-columns: 1fr;
    }

    .study-layout.notes-open {
        grid-template-columns: 1fr 400px;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: -300px;
        width: 300px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .mobile-menu-btn {
        display: block;
    }

    .mobile-overlay.show {
        display: block;
    }
}

@media (max-width: 768px) {
    .study-layout.notes-open {
        grid-template-columns: 1fr;
    }

    .notes-panel {
        position: fixed;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100vh;
        z-index: 1001;
        transition: right 0.3s ease;
    }

    .notes-panel.open {
        right: 0;
    }

    .main-header {
        padding: var(--spacing-md);
    }

    .header-title h1 {
        font-size: 1.25rem;
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .message-content {
        padding: var(--spacing-md);
    }

    .arabic-text {
        font-size: 1.25rem;
    }

    .chat-input-container {
        padding: var(--spacing-md);
    }

    .user-message .message-content,
    .ai-message .message-content {
        max-width: 90%;
    }
}

@media (max-width: 480px) {
    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .usage-limits {
        flex-direction: row;
        justify-content: space-around;
    }

    .header-actions {
        gap: var(--spacing-xs);
    }

    .header-actions .btn {
        padding: var(--spacing-sm);
    }

    .notes-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        min-width: 50%;
    }
}

/* Interactive Elements */
.typing-indicator .message-content {
    padding: var(--spacing-md) var(--spacing-lg);
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background-color: var(--text-muted);
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Notes Content Styles */
.notes-summary h4 {
    color: var(--primary-color);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-md);
}

.notes-summary ul {
    list-style: none;
    padding: 0;
    margin-bottom: var(--spacing-lg);
}

.notes-summary li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: var(--spacing-lg);
}

.notes-summary li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.verse-summary {
    background-color: var(--surface-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin: var(--spacing-md) 0;
}

.verse-summary p {
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Flashcard Styles */
.flashcard-container {
    max-width: 100%;
}

.flashcard {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.flashcard h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.flashcard p {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.flip-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.flip-btn:hover {
    background-color: var(--primary-hover);
}

.flashcard-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

/* Quiz Styles */
.quiz-container {
    max-width: 100%;
}

.quiz-question h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.quiz-question p {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.quiz-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-option:hover {
    background-color: var(--border-color);
}

.quiz-option input[type="radio"] {
    margin: 0;
}

.quiz-submit {
    width: 100%;
    padding: var(--spacing-md);
}

/* Games Styles */
.games-container {
    max-width: 100%;
}

.memory-game h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.memory-game p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
}

.game-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.game-card {
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-card:hover {
    border-color: var(--primary-color);
    background-color: var(--background-color);
}

.game-card.matched {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.card-content {
    font-size: 0.875rem;
    line-height: 1.4;
}

.game-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background-color: var(--surface-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
}
