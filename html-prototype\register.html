<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Tadabbur AI</title>
    <link rel="stylesheet" href="styles/global.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="auth-header">
        <div class="container">
            <a href="index.html" class="logo">
                <img src="../docs/logo.png" alt="Tadabbur AI" class="logo-img">
                <span><PERSON><PERSON><PERSON> AI</span>
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <!-- Form Header -->
                <div class="auth-header-content">
                    <h1>Create Account</h1>
                    <p>Start your journey of Quranic contemplation</p>
                </div>

                <!-- Register Form -->
                <form class="auth-form" id="registerForm">
                    <div class="form-group">
                        <label for="fullName">Full Name</label>
                        <input 
                            type="text" 
                            id="fullName" 
                            name="fullName" 
                            class="input" 
                            placeholder="Enter your full name"
                            required
                        >
                        <div class="error-message" id="fullNameError"></div>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="input" 
                            placeholder="Enter your email address"
                            required
                        >
                        <div class="error-message" id="emailError"></div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="input" 
                                placeholder="Create a strong password"
                                required
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength"></div>
                        <div class="error-message" id="passwordError"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                name="confirmPassword" 
                                class="input" 
                                placeholder="Confirm your password"
                                required
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                            </button>
                        </div>
                        <div class="error-message" id="confirmPasswordError"></div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-container">
                            <input type="checkbox" name="terms" id="terms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#terms" target="_blank">Terms of Service</a> and <a href="#privacy" target="_blank">Privacy Policy</a>
                        </label>
                        <div class="error-message" id="termsError"></div>
                    </div>

                    <button type="submit" class="btn btn-primary auth-submit" id="registerButton">
                        <span class="button-text">Create Account</span>
                        <div class="spinner" style="display: none;"></div>
                    </button>

                    <div class="form-error" id="formError" style="display: none;"></div>
                </form>

                <!-- Divider -->
                <div class="auth-divider">
                    <span>or</span>
                </div>

                <!-- Social Login (Future Feature) -->
                <div class="social-login">
                    <button class="btn btn-secondary social-btn" disabled>
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </button>
                </div>

                <!-- Switch to Login -->
                <div class="auth-switch">
                    <p>Already have an account? <a href="login.html">Sign in here</a></p>
                </div>
            </div>

            <!-- Side Info -->
            <div class="auth-info">
                <div class="info-content">
                    <h2>Join Our Community</h2>
                    <p>Experience a new way to study and understand the Quran with AI-powered insights and personalized learning tools.</p>
                    
                    <div class="features-list">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Join thousands of learners</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span>Personalized learning path</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-chart-line"></i>
                            <span>Track your progress</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Secure and private</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });

        // Form handling
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous errors
            clearErrors();
            
            // Get form data
            const formData = new FormData(this);
            const fullName = formData.get('fullName').trim();
            const email = formData.get('email').trim();
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            const terms = formData.get('terms');
            
            // Validation
            let hasErrors = false;
            
            if (!fullName) {
                showError('fullNameError', 'Full name is required');
                hasErrors = true;
            }
            
            if (!email) {
                showError('emailError', 'Email is required');
                hasErrors = true;
            } else if (!isValidEmail(email)) {
                showError('emailError', 'Please enter a valid email address');
                hasErrors = true;
            }
            
            if (!password) {
                showError('passwordError', 'Password is required');
                hasErrors = true;
            } else if (password.length < 8) {
                showError('passwordError', 'Password must be at least 8 characters long');
                hasErrors = true;
            }
            
            if (!confirmPassword) {
                showError('confirmPasswordError', 'Please confirm your password');
                hasErrors = true;
            } else if (password !== confirmPassword) {
                showError('confirmPasswordError', 'Passwords do not match');
                hasErrors = true;
            }
            
            if (!terms) {
                showError('termsError', 'You must agree to the terms and privacy policy');
                hasErrors = true;
            }
            
            if (hasErrors) return;
            
            // Show loading state
            showLoading(true);
            
            // Simulate API call
            setTimeout(() => {
                // For demo purposes, redirect to study page
                window.location.href = 'study.html';
            }, 2000);
        });
        
        function checkPasswordStrength(password) {
            const strengthElement = document.getElementById('passwordStrength');
            let strength = 0;
            let feedback = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    feedback = '<span class="strength-weak">Weak password</span>';
                    break;
                case 2:
                case 3:
                    feedback = '<span class="strength-medium">Medium password</span>';
                    break;
                case 4:
                case 5:
                    feedback = '<span class="strength-strong">Strong password</span>';
                    break;
            }
            
            strengthElement.innerHTML = feedback;
        }
        
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + 'ToggleIcon');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
        
        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }
        
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
        
        function clearErrors() {
            const errorElements = document.querySelectorAll('.error-message, .form-error');
            errorElements.forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }
        
        function showLoading(isLoading) {
            const button = document.getElementById('registerButton');
            const buttonText = button.querySelector('.button-text');
            const spinner = button.querySelector('.spinner');
            
            if (isLoading) {
                button.disabled = true;
                buttonText.style.display = 'none';
                spinner.style.display = 'block';
            } else {
                button.disabled = false;
                buttonText.style.display = 'block';
                spinner.style.display = 'none';
            }
        }
    </script>
</body>
</html>
