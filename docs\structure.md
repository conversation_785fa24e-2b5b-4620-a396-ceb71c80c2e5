# <PERSON><PERSON>bur AI - MVP Page Specifications

Complete breakdown of all 3 MVP pages based on discussion requirements.

## 1. HOME PAGE

### Layout & Style
- Clean, minimalist design similar to V0/Vibe interface
- Tadabbur AI logo prominently displayed at top
- Centered layout with focus on search functionality

### Main Elements
- Large search bar in center with placeholder text
- Submit button (arrow icon) on the right side of search bar
- Tagline: Something like "Deep Quranic contemplation powered by AI"

### Suggestion Chips (below search bar)
- "Explain Surah Al-Fatiha"
- "Verses about patience"
- "What is jihad in Quran?"
- "Stories of Prophet Yusuf"
- "Meaning of Al-Baqarah 255"

### Header
- Logo on left
- Login/Register buttons on right (if not logged in)
- User profile icon (if logged in)

### Footer
- Simple links (About, Contact, Terms, Privacy)

---

## 2. LOGIN/REGISTER PAGE

### Authentication System
- Using Better Auth for authentication
- Clean, modern forms

### Login Form
- Email/Username field
- Password field
- "Remember me" checkbox
- "Forgot password?" link
- Login button
- "Don't have an account? Register" link

### Register Form
- Full name field
- Email field
- Password field
- Confirm password field
- Terms & Privacy acceptance checkbox
- Register button
- "Already have an account? Login" link

### Additional Features
- Form validation
- Loading states
- Error message displays
- Responsive design

---

## 3. STUDY PAGE (Dashboard)

### Layout Structure
- **Left Sidebar** (collapsible)
- **Main Chat Area** (center)
- **Notes Panel** (right side, appears when generating notes)

### Left Sidebar

#### Profile Header
- User name and avatar
- Current plan display (Free/Premium)
- Usage limits: "3/10 chats today" and "2/3 notes total" (for free plan)

#### Folders Section
- Expandable folder tree structure
- Default folders: "Recent Notes", "Favorites"
- Custom folders user can create
- Each folder shows note count
- Drag and drop functionality for organization

#### Navigation
- "New Chat" button
- "All Chats" section showing chat history
- Each chat shows title (auto-generated) and timestamp

### Main Chat Area

#### Chat Interface
- Conversation history from homepage search
- User messages on right, AI responses on left
- Clean, readable typography
- Arabic text properly rendered with appropriate fonts

#### AI Response Format
- Quranic verses in Arabic with translation
- Contextual explanation from AI
- **"Generate Notes" button** at bottom of each AI response
- Source citations (Tafsir, translation, asbab al-nuzul references)

#### Chat Input
- Search/question input at bottom
- Send button
- Auto-resize text area

### Right Notes Panel (appears when generating notes)

#### Panel Behavior
- Slides in from right when "Generate Notes" clicked
- Takes up ~30-40% of screen width
- Can be closed/minimized

#### Tabbed Content
- **Summary Tab**: AI-generated summary of the verses
- **Flashcards Tab**: Question/answer pairs for memorization
- **Quiz Tab**: Multiple choice questions
- **Memory Games Tab**: Interactive memory exercises

#### Notes Actions
- Save notes to specific folder
- Title/rename notes
- Export options (future feature)
- Share notes (future feature)

### Special Behaviors

#### Auto-Save
- Every chat automatically saved
- Notes auto-saved when generated

#### One-Note-Per-Chat Rule
- When user clicks "Generate Notes", automatically creates new focused chat
- Copies the question and answer to new chat
- Generates notes in the new chat's right panel
- Shows brief loading message: "Creating focused study session..."

#### Responsive Design
- Mobile: Sidebar becomes overlay
- Tablet: Adjusted proportions
- Desktop: Full three-column layout

### Plan Limitations (Free Tier)
- 10 chats per day
- 3 notes total (lifetime)
- Upgrade prompts when limits reached

---

## Tech Stack Summary

### Frontend
- **Framework**: Next.js
- **Authentication**: Better Auth
- **Database**: Supabase
- **ORM**: Drizzle